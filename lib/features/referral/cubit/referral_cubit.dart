import 'package:injectable/injectable.dart';
import '../../../core/bloc/base_cubit.dart';
import '../../../core/errors/exceptions.dart';
import '../../auth/services/token_service.dart';
import '../services/referral_service.dart';
import 'referral_state.dart';

/// Cubit for managing referral code functionality
@injectable
class ReferralCubit extends BaseCubit<ReferralState> {
  final ReferralService _referralService;
  final TokenService _tokenService;

  ReferralCubit(this._referralService, this._tokenService) : super(const ReferralInitial()) {
    _checkExistingReferralCode();
  }

  /// Check if user already has a referral code from login
  Future<void> _checkExistingReferralCode() async {
    try {
      final user = await _tokenService.getUserData();
      if (user?.referralCode != null && user!.referralCode!.isNotEmpty) {
        emit(ReferralAlreadyHasCode(
          referralCode: user.referralCode!,
          message: '<PERSON>ạn đã có mã giới thiệu: ${user.referralCode}',
        ));
      }
    } catch (e) {
      // If error getting user data, stay in initial state
      // User can still manually enter referral code
    }
  }

  /// Apply referral code
  Future<void> applyReferralCode(String referralCode) async {
    if (referralCode.trim().isEmpty) {
      emit(const ReferralError(
        message: 'Vui lòng nhập mã giới thiệu',
        errorCode: 'EMPTY_CODE',
      ));
      return;
    }

    await executeWithErrorHandling(
      () async {
        emit(const ReferralLoading(loadingMessage: 'Đang xử lý mã giới thiệu...'));
        
        final response = await _referralService.applyReferralCode(referralCode.trim());
        
        emit(ReferralSuccess(
          data: response,
          message: response.message,
        ));
      },
    );
  }

  /// Clear error state when user starts typing again
  void clearError() {
    if (state is ReferralError) {
      emit(const ReferralInitial());
    }
  }

  /// Reset to initial state
  @override
  void reset() {
    emit(const ReferralInitial());
  }

  /// Create error state specific to referral
  @override
  ReferralError createErrorState({
    required String message,
    String? errorCode,
    dynamic originalError,
    StackTrace? stackTrace,
  }) {
    // Handle specific API errors
    if (originalError is ServerException) {
      final serverError = originalError;
      
      // Map common error codes to user-friendly messages
      String userMessage = message;
      switch (serverError.statusCode) {
        case 400:
          userMessage = 'Mã giới thiệu không hợp lệ hoặc đã hết hạn. Vui lòng kiểm tra lại.';
          break;
        case 401:
          userMessage = 'Phiên đăng nhập đã hết hạn. Vui lòng đăng nhập lại.';
          break;
        case 403:
          userMessage = 'Bạn không có quyền sử dụng mã giới thiệu này.';
          break;
        case 404:
          userMessage = 'Mã giới thiệu không tồn tại. Vui lòng kiểm tra lại.';
          break;
        case 409:
          userMessage = 'Bạn đã sử dụng mã giới thiệu này rồi hoặc đã có mã khác.';
          break;
        case 422:
          userMessage = 'Định dạng mã giới thiệu không đúng. Vui lòng nhập lại.';
          break;
        case 429:
          userMessage = 'Bạn đã thử quá nhiều lần. Vui lòng đợi một chút rồi thử lại.';
          break;
        case 500:
        case 502:
        case 503:
          userMessage = 'Lỗi hệ thống. Vui lòng thử lại sau ít phút.';
          break;
        default:
          userMessage = serverError.message.isNotEmpty
            ? serverError.message
            : 'Có lỗi xảy ra. Vui lòng thử lại sau.';
      }
      
      return ReferralError(
        message: userMessage,
        errorCode: errorCode ?? serverError.statusCode?.toString(),
        originalError: originalError,
        stackTrace: stackTrace,
      );
    } else if (originalError is NetworkException) {
      return ReferralError(
        message: 'Không có kết nối mạng. Vui lòng kiểm tra và thử lại.',
        errorCode: errorCode ?? 'NETWORK_ERROR',
        originalError: originalError,
        stackTrace: stackTrace,
      );
    }

    return ReferralError(
      message: message,
      errorCode: errorCode,
      originalError: originalError,
      stackTrace: stackTrace,
    );
  }

  /// Create loading state specific to referral
  @override
  ReferralLoading createLoadingState({String? message}) {
    return ReferralLoading(loadingMessage: message);
  }


}

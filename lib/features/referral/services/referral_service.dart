import 'package:banachef/core/services/logger/logger.dart';
import 'package:injectable/injectable.dart';
import 'package:logger/logger.dart';
import '../../../core/api/api_client.dart';
import '../../../core/errors/exceptions.dart';
import '../models/referral_models.dart';

/// Abstract interface for referral service
abstract class ReferralService {
  /// Apply referral code
  Future<ReferralResponse> applyReferralCode(String referralCode);
}

/// Implementation of referral service
@Singleton(as: ReferralService)
class ReferralServiceImpl implements ReferralService {
  final ApiClient _apiClient;
  final Logger _logger = AppLogger.log;

  ReferralServiceImpl(this._apiClient);

  @override
  Future<ReferralResponse> applyReferralCode(String referralCode) async {
    try {
      _logger.d('Applying referral code: $referralCode');

      final request = ReferralRequest(referralCode: referralCode);

      final response = await _apiClient.post<Map<String, dynamic>>(
        'referrals/apply',
        data: request.toJson(),
      );

      if (response.statusCode == 200 && response.data != null) {
        final referralResponse = ReferralResponse.fromJson(response.data!);
        _logger.d('Referral code applied successfully: ${referralResponse.message}');
        return referralResponse;
      } else {
        _logger.e('Unexpected response format. Status: ${response.statusCode}, Data: ${response.data}');
        throw ServerException(
          'Unexpected response format',
          statusCode: response.statusCode,
        );
      }
    } catch (e, stackTrace) {
      _logger.e('Error applying referral code: $e', error: e, stackTrace: stackTrace);

      // Re-throw known exceptions
      if (e is ServerException || e is NetworkException) {
        rethrow;
      }

      // Handle parsing errors
      if (e is FormatException || e.toString().contains('FormatException')) {
        throw ServerException(
          'Dữ liệu phản hồi từ server không hợp lệ',
          statusCode: 500,
        );
      }

      // Handle timeout errors
      if (e.toString().contains('timeout') || e.toString().contains('TimeoutException')) {
        throw NetworkException(
          'Kết nối bị timeout. Vui lòng thử lại.',
        );
      }

      // Handle other unknown errors
      throw ServerException(
        'Có lỗi xảy ra khi xử lý mã giới thiệu. Vui lòng thử lại sau.',
        statusCode: 500,
      );
    }
  }
}

import 'dart:async';

import 'package:auto_route/auto_route.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:banachef/core/bloc/bloc_exports.dart';
import 'package:banachef/core/theme/app_colors.dart';
import 'package:banachef/shared/assets/assets.gen.dart';
import 'package:banachef/ui/responsive/responsive.dart';
import '../cubit/splash_cubit.dart';
import '../cubit/splash_state.dart';

@RoutePage()
class SplashPage extends StatefulWidget {
  const SplashPage({super.key});

  @override
  State<SplashPage> createState() => _SplashPageState();
}

class _SplashPageState extends State<SplashPage> {
  Timer? _navigationTimer;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Trong debug mode, skip splash nhanh hơn để tránh hot reload quay về splash
      final duration = kDebugMode
          ? const Duration(milliseconds: 500)
          : const Duration(milliseconds: 3000);

      _navigationTimer = Timer(duration, () {
        if (mounted) {
          context.router.replacePath('/onboarding');
        }
      });
    });
  }

  @override
  void dispose() {
    _navigationTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return const _SplashView();
  }
}

class _SplashView extends StatelessWidget {
  const _SplashView();

  @override
  Widget build(BuildContext context) {
    return CupertinoPageScaffold(
      backgroundColor: AppColors.accent, // Xanh Lá Tươi (#6ECB63)
      child: SafeArea(
        child: SizedBox.expand(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Spacer to push content to center
              const Spacer(flex: 2),
              
              // AppIcon Lottie Animation - Center of screen
              _buildAppIcon(context),
              
              // Spacer between icon and label
              const Spacer(flex: 1),
              
              // "Design by Banana Care" label - Bottom center
              _buildBrandLabel(context),
              
              // Bottom spacer
              const Spacer(flex: 1),
            ],
          ),
        ),
      ),
    );
  }

  /// Build the app icon lottie animation
  Widget _buildAppIcon(BuildContext context) {
    return SizedBox(
      width: context.isMobile ? 120 : 150,
      height: context.isMobile ? 120 : 150,
      child: Assets.lottie.appIcon.lottie(
        key: const Key('app_icon_lottie'),
        animate: true,
        repeat: false,
        fit: BoxFit.contain,
      ),
    );
  }

  /// Build the brand label
  Widget _buildBrandLabel(BuildContext context) {
    return Padding(
      padding: context.pagePadding,
      child: Text(
        'Design by Banana Care',
        style: context.bodyLarge.copyWith(
          color: const Color(0xFFFFFFFF),
          fontWeight: FontWeight.w500,
          letterSpacing: 0.5,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }
}

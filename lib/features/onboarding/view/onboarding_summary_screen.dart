import 'package:auto_route/auto_route.dart';
import '../../../core/bloc/bloc_exports.dart';
import '../../../ui/kit/ui_kit.dart';
import '../cubit/onboarding_summary_cubit.dart';
import '../cubit/onboarding_summary_state.dart';
import '../models/onboarding_data.dart';
import '../widgets/summary/summary_header.dart';
import '../widgets/summary/meal_plan_preview.dart';
import '../widgets/summary/profile_summary.dart';
import '../widgets/summary/summary_cta.dart';

@RoutePage(name: 'OnboardingSummaryRoute')
class OnboardingSummaryScreen extends BaseCubitView<OnboardingSummaryCubit> {
  final OnboardingData onboardingData;
  final String userName;

  const OnboardingSummaryScreen({
    super.key,
    required this.onboardingData,
    required this.userName,
  });

  @override
  OnboardingSummaryCubit createCubit(BuildContext context) {
    final cubit = OnboardingSummaryCubit();
    // Generate summary data when screen loads
    WidgetsBinding.instance.addPostFrameCallback((_) {
      cubit.generateSummary(onboardingData, userName);
    });
    return cubit;
  }

  @override
  Widget buildContent(BuildContext context, BaseState state) {
    return const _OnboardingSummaryContent();
  }

  @override
  Widget buildLoading(BuildContext context, BaseLoadingState state) {
    return Scaffold(
      backgroundColor: BanaColors.background,
      body: SafeArea(
        child: ResponsiveContainer(
          padding: BanaSpacing.all.lg,
          child: Column(
            children: [
              // Header with user name
              SummaryHeader(userName: userName),

              const Spacer(),

              // Loading indicator
              Column(
                children: [
                  const CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(BanaColors.primary),
                  ),

                  BanaSpacing.verticalSpacing.lg,

                  Text(
                    state is OnboardingSummaryLoading
                        ? state.loadingMessage ?? 'Đang tải...'
                        : 'Đang tải...',
                    style: BanaTypography.bodyLarge.copyWith(
                      color: BanaColors.textSecondary,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),

              const Spacer(),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget buildError(BuildContext context, BaseErrorState state) {
    return Scaffold(
      backgroundColor: BanaColors.background,
      body: SafeArea(
        child: ResponsiveContainer(
          padding: BanaSpacing.all.lg,
          child: Column(
            children: [
              // Header with user name
              SummaryHeader(userName: userName),

              const Spacer(),

              // Error content
              Column(
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: BanaColors.error,
                  ),

                  BanaSpacing.verticalSpacing.lg,

                  Text(
                    state.message,
                    style: BanaTypography.bodyLarge.copyWith(
                      color: BanaColors.text,
                    ),
                    textAlign: TextAlign.center,
                  ),

                  BanaSpacing.verticalSpacing.xl,

                  BanaButton.primary(
                    text: 'Thử lại',
                    onPressed: () {
                      final cubit = BlocProvider.of<OnboardingSummaryCubit>(context);
                      cubit.generateSummary(onboardingData, userName);
                    },
                  ),
                ],
              ),

              const Spacer(),
            ],
          ),
        ),
      ),
    );
  }
}

class _OnboardingSummaryContent extends StatelessWidget {
  const _OnboardingSummaryContent();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: BanaColors.background,
      body: SafeArea(
        child: BlocBuilder<OnboardingSummaryCubit, OnboardingSummaryState>(
          builder: (context, state) {
            if (state is OnboardingSummarySuccess) {
              return _buildSuccessContent(context, state.summaryData);
            }
            
            // This should not happen as loading/error states are handled by BaseCubitView
            return const SizedBox.shrink();
          },
        ),
      ),
    );
  }

  Widget _buildSuccessContent(BuildContext context, OnboardingSummaryData data) {
    return Column(
      children: [
        // Scrollable content
        Expanded(
          child: SingleChildScrollView(
            child: ResponsiveContainer(
              padding: BanaSpacing.all.lg,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header with welcome message
                  SummaryHeader(userName: data.userName),

                  BanaSpacing.verticalSpacing.xl,

                  // Meal plan preview
                  MealPlanPreview(mealPlan: data.mealPlan),

                  BanaSpacing.verticalSpacing.xl,

                  // Profile summary
                  ProfileSummary(onboardingData: data.onboardingData),

                  // Bottom spacing for CTA
                  BanaSpacing.verticalSpacing.xl,
                ],
              ),
            ),
          ),
        ),

        // Fixed CTA at bottom
        SummaryCTA(
          onPressed: () => _navigateToDashboard(context),
        ),
      ],
    );
  }

  void _navigateToDashboard(BuildContext context) {
    // Navigate to dashboard and clear all previous routes
    context.router.replacePath('/dashboard');
  }
}

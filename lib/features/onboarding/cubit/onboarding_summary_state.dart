import '../../../core/bloc/bloc_exports.dart';
import '../models/onboarding_data.dart';

/// Base class for onboarding summary states
abstract class OnboardingSummaryState extends BaseState {
  const OnboardingSummaryState();
}

/// Initial state
class OnboardingSummaryInitial extends OnboardingSummaryState implements BaseInitialState {
  const OnboardingSummaryInitial();

  @override
  String toString() => 'OnboardingSummaryInitial';
}

/// Loading state when generating meal plan
class OnboardingSummaryLoading extends OnboardingSummaryState implements BaseLoadingState {
  final String? loadingMessage;
  
  const OnboardingSummaryLoading({this.loadingMessage});

  @override
  bool get isLoading => true;

  @override
  List<Object?> get props => [loadingMessage];

  @override
  String toString() => 'OnboardingSummaryLoading(message: $loadingMessage)';
}

/// Success state with generated meal plan and summary data
class OnboardingSummarySuccess extends OnboardingSummaryState implements BaseSuccessState<OnboardingSummaryData> {
  final OnboardingSummaryData _data;
  @override
  final String? message;

  const OnboardingSummarySuccess(
    this._data, {
    this.message,
  });

  @override
  OnboardingSummaryData? get data => _data;

  // Getter for non-nullable access
  OnboardingSummaryData get summaryData => _data;

  @override
  bool get isSuccess => true;

  @override
  List<Object?> get props => [_data, message];

  @override
  String toString() => 'OnboardingSummarySuccess(data: $_data, message: $message)';
}

/// Error state
class OnboardingSummaryError extends OnboardingSummaryState implements BaseErrorState {
  @override
  final String message;
  @override
  final Object? originalError;
  @override
  final StackTrace? stackTrace;
  @override
  final String? errorCode;

  const OnboardingSummaryError({
    required this.message,
    this.originalError,
    this.stackTrace,
    this.errorCode,
  });

  @override
  bool get isError => true;

  @override
  List<Object?> get props => [message, originalError, stackTrace, errorCode];

  @override
  String toString() => 'OnboardingSummaryError(message: $message, errorCode: $errorCode)';
}

/// Data model for summary screen
class OnboardingSummaryData extends Equatable {
  final OnboardingData onboardingData;
  final List<MealPlanItem> mealPlan;
  final String userName;

  const OnboardingSummaryData({
    required this.onboardingData,
    required this.mealPlan,
    required this.userName,
  });

  @override
  List<Object?> get props => [onboardingData, mealPlan, userName];

  @override
  String toString() => 'OnboardingSummaryData(userName: $userName, mealPlan: ${mealPlan.length} items)';
}

/// Model for meal plan items
class MealPlanItem extends Equatable {
  final String mealType; // 'Bữa Sáng', 'Bữa Trưa', 'Bữa Tối'
  final String dishName;
  final String? imageUrl;
  final String chefNote; // Ghi chú của Chef Bana về lý do chọn món này

  const MealPlanItem({
    required this.mealType,
    required this.dishName,
    this.imageUrl,
    required this.chefNote,
  });

  @override
  List<Object?> get props => [mealType, dishName, imageUrl, chefNote];

  @override
  String toString() => 'MealPlanItem(mealType: $mealType, dishName: $dishName)';
}

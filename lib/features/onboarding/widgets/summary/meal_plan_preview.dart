import '../../../../ui/kit/ui_kit.dart';
import '../../cubit/onboarding_summary_state.dart';

/// Widget to display meal plan preview with Chef <PERSON>'s recommendations
class MealPlanPreview extends StatelessWidget {
  final List<MealPlanItem> mealPlan;

  const MealPlanPreview({
    super.key,
    required this.mealPlan,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section title
        Text(
          'Đ<PERSON>y là <PERSON>ế hoạch Gợi ý cho Ngày mai của bạn',
          style: BanaTypography.title2.copyWith(
            color: BanaColors.text,
          ),
        ),

        BanaSpacing.verticalSpacing.lg,

        // Meal plan cards
        ...mealPlan.map((meal) => _buildMealCard(meal)),
      ],
    );
  }

  Widget _buildMealCard(MealPlanItem meal) {
    return Container(
      margin: EdgeInsets.only(bottom: BanaSpacing.lg),
      child: Container(
        decoration: BoxDecoration(
          color: BanaColors.surface,
          borderRadius: BanaBorders.radius.lg,
          boxShadow: BanaShadows.card,
        ),
        child: Padding(
          padding: BanaSpacing.all.lg,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Meal type and dish name
              Row(
                children: [
                  // Meal type badge
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: BanaSpacing.sm,
                      vertical: BanaSpacing.xs,
                    ),
                    decoration: BoxDecoration(
                      color: _getMealTypeColor(meal.mealType),
                      borderRadius: BanaBorders.radius.sm,
                    ),
                    child: Text(
                      meal.mealType,
                      style: BanaTypography.caption.copyWith(
                        color: BanaColors.surface,
                        fontWeight: BanaTypography.semibold,
                      ),
                    ),
                  ),

                  BanaSpacing.horizontalSpacing.md,

                  // Dish name
                  Expanded(
                    child: Text(
                      meal.dishName,
                      style: BanaTypography.title3.copyWith(
                        color: BanaColors.text,
                      ),
                    ),
                  ),
                ],
              ),

              BanaSpacing.verticalSpacing.md,

              // Chef Bana's note
              Container(
                padding: BanaSpacing.all.md,
                decoration: BoxDecoration(
                  color: BanaColors.primary.withValues(alpha: 0.1),
                  borderRadius: BanaBorders.radius.md,
                ),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Chef icon
                    Text(
                      '👨‍🍳',
                      style: TextStyle(fontSize: 16),
                    ),

                    BanaSpacing.horizontalSpacing.sm,

                    // Note text
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Ghi chú của Chef Bana:',
                            style: BanaTypography.caption.copyWith(
                              color: BanaColors.textSecondary,
                              fontWeight: BanaTypography.semibold,
                            ),
                          ),
                          
                          BanaSpacing.verticalSpacing.xs,
                          
                          Text(
                            meal.chefNote,
                            style: BanaTypography.bodyMedium.copyWith(
                              color: BanaColors.text,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getMealTypeColor(String mealType) {
    switch (mealType) {
      case 'Bữa Sáng':
        return BanaColors.warning; // Yellow for breakfast
      case 'Bữa Trưa':
        return BanaColors.primary; // Banana yellow for lunch
      case 'Bữa Tối':
        return BanaColors.success; // Green for dinner
      default:
        return BanaColors.textSecondary;
    }
  }
}

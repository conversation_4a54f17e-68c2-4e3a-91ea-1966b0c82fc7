import 'package:flutter/services.dart';
import '../../../../ui/kit/ui_kit.dart';

/// Call-to-action widget for summary screen
class SummaryCTA extends StatelessWidget {
  final VoidCallback onPressed;
  final bool isLoading;

  const SummaryCTA({
    super.key,
    required this.onPressed,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: BanaSpacing.all.lg,
      child: BanaButton.primary(
        text: '<PERSON><PERSON><PERSON> bếp cùng Chef Bana!',
        onPressed: isLoading ? null : () {
          // Haptic feedback for better UX
          HapticFeedback.lightImpact();
          onPressed();
        },
        isFullWidth: true,
        isLoading: isLoading,
        size: BanaButtonSize.large,
      ),
    );
  }
}

enum Environment {
  development,
  production,
}

class AppConfig {
  static const String appName = 'BanaChef';
  static const String appVersion = '1.0.0';

  // Current environment - change this to switch environments
  static const Environment currentEnvironment = Environment.development;

  // API Configuration
  static String get baseUrl {
    switch (currentEnvironment) {
      case Environment.development:
        return 'http://192.168.1.82:8000/api/v1/';
      case Environment.production:
        return 'https://api.banachef.com/v1/';
    }
  }

  static const Duration apiTimeout = Duration(seconds: 30);

  // SharedPreferences Keys (for app settings only)
  static const String themeKey = 'theme_mode';
  static const String languageKey = 'language';
  static const String onboardingKey = 'onboarding_completed';

  // Database Configuration
  static String get databaseName {
    switch (currentEnvironment) {
      case Environment.development:
        return 'banachef_dev.db';
      case Environment.production:
        return 'banachef.db';
    }
  }

  static const int databaseVersion = 1;

  // Pagination
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;

  // Image Configuration
  static const int maxImageSize = 5 * 1024 * 1024; // 5MB
  static const List<String> allowedImageFormats = ['jpg', 'jpeg', 'png'];

  // Cache Configuration
  static const Duration cacheExpiration = Duration(hours: 24);
  static const int maxCacheSize = 100 * 1024 * 1024; // 100MB

  // Feature Flags
  static bool get enableAnalytics {
    switch (currentEnvironment) {
      case Environment.development:
        return false;
      case Environment.production:
        return true;
    }
  }

  static bool get enableCrashReporting {
    switch (currentEnvironment) {
      case Environment.development:
        return false;
      case Environment.production:
        return true;
    }
  }

  static bool get enableDebugMode {
    switch (currentEnvironment) {
      case Environment.development:
        return true;
      case Environment.production:
        return false;
    }
  }

  // Environment helpers
  static bool get isDevelopment => currentEnvironment == Environment.development;
  static bool get isProduction => currentEnvironment == Environment.production;
  static String get environmentName => currentEnvironment.name;

  // Google Sign-In Configuration
  static String get googleClientId {
    switch (currentEnvironment) {
      case Environment.development:
        return '418965755087-8ulqb0lq7fgvofmpgrdaia3c6ho7u1mc.apps.googleusercontent.com'; //Web client ID
      case Environment.production:
        return '418965755087-8ulqb0lq7fgvofmpgrdaia3c6ho7u1mc.apps.googleusercontent.com';
    }
  }

}

import 'package:flutter/cupertino.dart';
import '../../tokens/bana_colors.dart';
import '../../tokens/bana_typography.dart';
import '../../tokens/bana_spacing.dart';
import '../../tokens/bana_shadows.dart';
import '../../tokens/bana_borders.dart';
import '../buttons/bana_icon_button.dart';

/// BanaChef AI Toast Component
/// 
/// Toast notification component following the BanaChef Design System.
/// Used for quick feedback messages that don't require user action.
/// 
/// Usage:
/// ```dart
/// // Success toast
/// BanaToast.success(
///   context: context,
///   message: 'Recipe saved successfully!',
/// )
/// 
/// // Error toast
/// BanaToast.error(
///   context: context,
///   message: 'Failed to save recipe',
/// )
/// 
/// // Custom toast
/// BanaToast.show(
///   context: context,
///   message: 'Custom message',
///   icon: Icons.info,
///   backgroundColor: Colors.blue,
/// )
/// ```
class BanaToast {
  BanaToast._(); // Private constructor

  /// Show a toast message
  static void show({
    required BuildContext context,
    required String message,
    IconData? icon,
    Color? backgroundColor,
    Color? textColor,
    Color? iconColor,
    Duration duration = const Duration(seconds: 3),
    BanaToastPosition position = BanaToastPosition.bottom,
    bool showCloseButton = false,
    VoidCallback? onTap,
  }) {
    final overlay = Overlay.of(context);
    late OverlayEntry overlayEntry;
    
    overlayEntry = OverlayEntry(
      builder: (context) => _BanaToastWidget(
        message: message,
        icon: icon,
        backgroundColor: backgroundColor,
        textColor: textColor,
        iconColor: iconColor,
        position: position,
        showCloseButton: showCloseButton,
        onTap: onTap,
        onDismiss: () => overlayEntry.remove(),
      ),
    );
    
    overlay.insert(overlayEntry);
    
    // Auto dismiss after duration
    Future.delayed(duration, () {
      if (overlayEntry.mounted) {
        overlayEntry.remove();
      }
    });
  }

  /// Show success toast
  static void success({
    required BuildContext context,
    required String message,
    Duration duration = const Duration(seconds: 3),
    BanaToastPosition position = BanaToastPosition.bottom,
    bool showCloseButton = false,
    VoidCallback? onTap,
  }) {
    show(
      context: context,
      message: message,
      icon: CupertinoIcons.check_mark_circled,
      backgroundColor: BanaColors.success,
      textColor: BanaColors.onSuccess,
      iconColor: BanaColors.onSuccess,
      duration: duration,
      position: position,
      showCloseButton: showCloseButton,
      onTap: onTap,
    );
  }

  /// Show error toast
  static void error({
    required BuildContext context,
    required String message,
    Duration duration = const Duration(seconds: 4),
    BanaToastPosition position = BanaToastPosition.bottom,
    bool showCloseButton = true,
    VoidCallback? onTap,
  }) {
    show(
      context: context,
      message: message,
      icon: CupertinoIcons.exclamationmark_circle,
      backgroundColor: BanaColors.error,
      textColor: BanaColors.onError,
      iconColor: BanaColors.onError,
      duration: duration,
      position: position,
      showCloseButton: showCloseButton,
      onTap: onTap,
    );
  }

  /// Show warning toast
  static void warning({
    required BuildContext context,
    required String message,
    Duration duration = const Duration(seconds: 3),
    BanaToastPosition position = BanaToastPosition.bottom,
    bool showCloseButton = false,
    VoidCallback? onTap,
  }) {
    show(
      context: context,
      message: message,
      icon: CupertinoIcons.exclamationmark_triangle,
      backgroundColor: BanaColors.warning,
      textColor: BanaColors.onWarning,
      iconColor: BanaColors.onWarning,
      duration: duration,
      position: position,
      showCloseButton: showCloseButton,
      onTap: onTap,
    );
  }

  /// Show info toast
  static void info({
    required BuildContext context,
    required String message,
    Duration duration = const Duration(seconds: 3),
    BanaToastPosition position = BanaToastPosition.bottom,
    bool showCloseButton = false,
    VoidCallback? onTap,
  }) {
    show(
      context: context,
      message: message,
      icon: CupertinoIcons.info_circle,
      backgroundColor: BanaColors.info,
      textColor: BanaColors.onInfo,
      iconColor: BanaColors.onInfo,
      duration: duration,
      position: position,
      showCloseButton: showCloseButton,
      onTap: onTap,
    );
  }
}

class _BanaToastWidget extends StatefulWidget {
  final String message;
  final IconData? icon;
  final Color? backgroundColor;
  final Color? textColor;
  final Color? iconColor;
  final BanaToastPosition position;
  final bool showCloseButton;
  final VoidCallback? onTap;
  final VoidCallback onDismiss;

  const _BanaToastWidget({
    required this.message,
    this.icon,
    this.backgroundColor,
    this.textColor,
    this.iconColor,
    required this.position,
    required this.showCloseButton,
    this.onTap,
    required this.onDismiss,
  });

  @override
  State<_BanaToastWidget> createState() => _BanaToastWidgetState();
}

class _BanaToastWidgetState extends State<_BanaToastWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _slideAnimation;
  late Animation<double> _opacityAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _slideAnimation = Tween<double>(
      begin: widget.position == BanaToastPosition.top ? -1.0 : 1.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));
    
    _opacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));
    
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _dismiss() async {
    await _animationController.reverse();
    widget.onDismiss();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Positioned(
          top: widget.position == BanaToastPosition.top 
              ? MediaQuery.of(context).padding.top + BanaSpacing.md
              : null,
          bottom: widget.position == BanaToastPosition.bottom 
              ? MediaQuery.of(context).padding.bottom + BanaSpacing.md
              : null,
          left: BanaSpacing.md,
          right: BanaSpacing.md,
          child: Transform.translate(
            offset: Offset(
              0,
              _slideAnimation.value * 100,
            ),
            child: Opacity(
              opacity: _opacityAnimation.value,
              child: _buildToast(),
            ),
          ),
        );
      },
    );
  }

  Widget _buildToast() {
    return GestureDetector(
      onTap: widget.onTap,
      child: Container(
        padding: BanaSpacing.all.md,
        decoration: BoxDecoration(
          color: widget.backgroundColor ?? BanaColors.text,
          borderRadius: BanaBorders.radius.md,
          boxShadow: BanaShadows.elevation3,
        ),
        child: Row(
          children: [
            // Icon
            if (widget.icon != null) ...[
              Icon(
                widget.icon,
                size: 20,
                color: widget.iconColor ?? BanaColors.surface,
              ),
              BanaSpacing.horizontalSpacing.sm,
            ],
            
            // Message
            Expanded(
              child: Text(
                widget.message,
                style: BanaTypography.bodyMedium.copyWith(
                  color: widget.textColor ?? BanaColors.surface,
                ),
              ),
            ),
            
            // Close button
            if (widget.showCloseButton) ...[
              BanaSpacing.horizontalSpacing.sm,
              BanaIconButton(
                icon: CupertinoIcons.xmark,
                onPressed: _dismiss,
                size: BanaIconButtonSize.small,
                iconColor: widget.textColor ?? BanaColors.surface,
                accessibilityLabel: 'Close toast',
              ),
            ],
          ],
        ),
      ),
    );
  }
}

/// Toast position options
enum BanaToastPosition {
  /// Show toast at the top of the screen
  top,
  
  /// Show toast at the bottom of the screen
  bottom,
}

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart' show Tooltip;
import 'package:flutter/services.dart';
import '../../tokens/bana_colors.dart';
import '../../tokens/bana_shadows.dart';
import '../../tokens/bana_borders.dart';

/// BanaChef AI Icon Button Component
/// 
/// Icon-only button component following the BanaChef Design System.
/// Optimized for accessibility with proper touch targets and labels.
/// 
/// Usage:
/// ```dart
/// // Standard icon button
/// BanaIconButton(
///   icon: Icons.favorite,
///   onPressed: () {},
///   accessibilityLabel: 'Add to favorites',
/// )
/// 
/// // Filled icon button
/// BanaIconButton.filled(
///   icon: Icons.close,
///   onPressed: () {},
///   accessibilityLabel: 'Close',
/// )
/// ```
class BanaIconButton extends StatefulWidget {
  /// Icon to display
  final IconData icon;
  
  /// Callback when button is pressed
  final VoidCallback? onPressed;
  
  /// Button variant type
  final BanaIconButtonType type;
  
  /// Button size
  final BanaIconButtonSize size;
  
  /// Icon color (overrides theme)
  final Color? iconColor;
  
  /// Background color (overrides theme)
  final Color? backgroundColor;
  
  /// Border radius (overrides theme)
  final double? borderRadius;
  
  /// Accessibility label for screen readers
  final String accessibilityLabel;
  
  /// Optional tooltip text
  final String? tooltip;

  const BanaIconButton({
    super.key,
    required this.icon,
    this.onPressed,
    this.type = BanaIconButtonType.standard,
    this.size = BanaIconButtonSize.medium,
    this.iconColor,
    this.backgroundColor,
    this.borderRadius,
    required this.accessibilityLabel,
    this.tooltip,
  });

  /// Standard icon button constructor
  const BanaIconButton.standard({
    super.key,
    required this.icon,
    this.onPressed,
    this.size = BanaIconButtonSize.medium,
    this.iconColor,
    this.backgroundColor,
    this.borderRadius,
    required this.accessibilityLabel,
    this.tooltip,
  }) : type = BanaIconButtonType.standard;

  /// Filled icon button constructor
  const BanaIconButton.filled({
    super.key,
    required this.icon,
    this.onPressed,
    this.size = BanaIconButtonSize.medium,
    this.iconColor,
    this.backgroundColor,
    this.borderRadius,
    required this.accessibilityLabel,
    this.tooltip,
  }) : type = BanaIconButtonType.filled;

  /// Outlined icon button constructor
  const BanaIconButton.outlined({
    super.key,
    required this.icon,
    this.onPressed,
    this.size = BanaIconButtonSize.medium,
    this.iconColor,
    this.backgroundColor,
    this.borderRadius,
    required this.accessibilityLabel,
    this.tooltip,
  }) : type = BanaIconButtonType.outlined;

  @override
  State<BanaIconButton> createState() => _BanaIconButtonState();
}

class _BanaIconButtonState extends State<BanaIconButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 100),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.9,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _onTapDown(TapDownDetails details) {
    if (widget.onPressed != null) {
      setState(() => _isPressed = true);
      _animationController.forward();
      HapticFeedback.lightImpact();
    }
  }

  void _onTapUp(TapUpDetails details) {
    _resetPressedState();
  }

  void _onTapCancel() {
    _resetPressedState();
  }

  void _resetPressedState() {
    if (_isPressed) {
      setState(() => _isPressed = false);
      _animationController.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    final isEnabled = widget.onPressed != null;
    final buttonSize = _getButtonSize();
    
    Widget button = AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: GestureDetector(
            onTapDown: _onTapDown,
            onTapUp: _onTapUp,
            onTapCancel: _onTapCancel,
            onTap: isEnabled ? widget.onPressed : null,
            child: Container(
              width: buttonSize,
              height: buttonSize,
              decoration: _getButtonDecoration(isEnabled),
              child: Center(
                child: Icon(
                  widget.icon,
                  size: _getIconSize(),
                  color: _getIconColor(isEnabled),
                ),
              ),
            ),
          ),
        );
      },
    );

    // Add semantic label for accessibility
    button = Semantics(
      label: widget.accessibilityLabel,
      button: true,
      enabled: isEnabled,
      child: button,
    );

    // Add tooltip if provided
    if (widget.tooltip != null) {
      button = Tooltip(
        message: widget.tooltip!,
        child: button,
      );
    }

    return button;
  }

  double _getButtonSize() {
    switch (widget.size) {
      case BanaIconButtonSize.small:
        return 32.0;
      case BanaIconButtonSize.medium:
        return 44.0; // Minimum touch target size
      case BanaIconButtonSize.large:
        return 56.0;
    }
  }

  double _getIconSize() {
    switch (widget.size) {
      case BanaIconButtonSize.small:
        return 16.0;
      case BanaIconButtonSize.medium:
        return 24.0;
      case BanaIconButtonSize.large:
        return 32.0;
    }
  }

  BoxDecoration _getButtonDecoration(bool isEnabled) {
    Color backgroundColor;
    List<BoxShadow> boxShadow = BanaShadows.none;
    Border? border;

    switch (widget.type) {
      case BanaIconButtonType.standard:
        backgroundColor = widget.backgroundColor ?? const Color(0x00000000);
        break;
      case BanaIconButtonType.filled:
        backgroundColor = widget.backgroundColor ?? 
            (isEnabled ? BanaColors.primary : BanaColors.disabled);
        boxShadow = isEnabled ? 
            (_isPressed ? BanaShadows.buttonPressed : BanaShadows.elevation1) : 
            BanaShadows.none;
        break;
      case BanaIconButtonType.outlined:
        backgroundColor = widget.backgroundColor ?? BanaColors.surface;
        border = Border.all(
          color: isEnabled ? BanaColors.border : BanaColors.disabled,
          width: BanaBorders.widthThin,
        );
        break;
    }

    return BoxDecoration(
      color: backgroundColor,
      borderRadius: BorderRadius.circular(
        widget.borderRadius ?? _getDefaultBorderRadius(),
      ),
      boxShadow: boxShadow,
      border: border,
    );
  }

  double _getDefaultBorderRadius() {
    switch (widget.type) {
      case BanaIconButtonType.standard:
        return BanaBorders.radiusSM;
      case BanaIconButtonType.filled:
        return BanaBorders.radiusCircular;
      case BanaIconButtonType.outlined:
        return BanaBorders.radiusSM;
    }
  }

  Color _getIconColor(bool isEnabled) {
    if (widget.iconColor != null) {
      return isEnabled ? widget.iconColor! : widget.iconColor!.withValues(alpha: 0.5);
    }

    switch (widget.type) {
      case BanaIconButtonType.standard:
        return isEnabled ? BanaColors.text : BanaColors.onDisabled;
      case BanaIconButtonType.filled:
        return isEnabled ? BanaColors.onPrimary : BanaColors.onDisabled;
      case BanaIconButtonType.outlined:
        return isEnabled ? BanaColors.text : BanaColors.onDisabled;
    }
  }
}

/// Icon button type variants
enum BanaIconButtonType {
  /// Standard icon button - Transparent background
  standard,
  
  /// Filled icon button - Primary background
  filled,
  
  /// Outlined icon button - Border with transparent background
  outlined,
}

/// Icon button size variants
enum BanaIconButtonSize {
  /// Small icon button - 32pt size
  small,
  
  /// Medium icon button - 44pt size (minimum touch target)
  medium,
  
  /// Large icon button - 56pt size
  large,
}

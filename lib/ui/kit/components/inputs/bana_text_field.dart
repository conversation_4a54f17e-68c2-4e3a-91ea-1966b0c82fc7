import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import '../../tokens/bana_colors.dart';
import '../../tokens/bana_typography.dart';
import '../../tokens/bana_spacing.dart';
import '../../tokens/bana_borders.dart';

/// BanaChef AI Text Field Component
/// 
/// Text input component following the BanaChef Design System.
/// Supports various states, validation, and accessibility features.
/// 
/// Usage:
/// ```dart
/// BanaTextField(
///   label: 'Recipe Name',
///   hint: 'Enter recipe name',
///   onChanged: (value) => setState(() => recipeName = value),
///   validator: (value) => value?.isEmpty == true ? 'Required' : null,
/// )
/// 
/// // Password field
/// BanaTextField.password(
///   label: 'Password',
///   hint: 'Enter your password',
/// )
/// 
/// // Multiline field
/// BanaTextField.multiline(
///   label: 'Instructions',
///   hint: 'Enter cooking instructions',
///   maxLines: 5,
/// )
/// ```
class BanaTextField extends StatefulWidget {
  /// Field label
  final String? label;
  
  /// Placeholder text
  final String? hint;
  
  /// Initial value
  final String? initialValue;
  
  /// Text controller
  final TextEditingController? controller;
  
  /// Whether field is enabled
  final bool enabled;
  
  /// Whether field is read-only
  final bool readOnly;
  
  /// Whether field is required
  final bool required;
  
  /// Whether to obscure text (for passwords)
  final bool obscureText;
  
  /// Maximum number of lines
  final int? maxLines;
  
  /// Minimum number of lines
  final int? minLines;
  
  /// Maximum character length
  final int? maxLength;
  
  /// Keyboard type
  final TextInputType keyboardType;
  
  /// Text input action
  final TextInputAction textInputAction;
  
  /// Input formatters
  final List<TextInputFormatter>? inputFormatters;
  
  /// Validation function
  final String? Function(String?)? validator;
  
  /// Callback when text changes
  final ValueChanged<String>? onChanged;
  
  /// Callback when field is submitted
  final ValueChanged<String>? onSubmitted;
  
  /// Callback when field is tapped
  final VoidCallback? onTap;
  
  /// Prefix icon
  final IconData? prefixIcon;
  
  /// Suffix icon
  final IconData? suffixIcon;
  
  /// Suffix icon callback
  final VoidCallback? onSuffixIconTap;
  
  /// Focus node
  final FocusNode? focusNode;
  
  /// Auto focus
  final bool autofocus;
  
  /// Field variant
  final BanaTextFieldVariant variant;

  /// Error message to display
  final String? errorText;

  /// Whether field has error state
  final bool hasError;

  const BanaTextField({
    super.key,
    this.label,
    this.hint,
    this.initialValue,
    this.controller,
    this.enabled = true,
    this.readOnly = false,
    this.required = false,
    this.obscureText = false,
    this.maxLines = 1,
    this.minLines,
    this.maxLength,
    this.keyboardType = TextInputType.text,
    this.textInputAction = TextInputAction.done,
    this.inputFormatters,
    this.validator,
    this.onChanged,
    this.onSubmitted,
    this.onTap,
    this.prefixIcon,
    this.suffixIcon,
    this.onSuffixIconTap,
    this.focusNode,
    this.autofocus = false,
    this.variant = BanaTextFieldVariant.outlined,
    this.errorText,
    this.hasError = false,
  });

  /// Password text field constructor
  const BanaTextField.password({
    super.key,
    this.label,
    this.hint,
    this.initialValue,
    this.controller,
    this.enabled = true,
    this.readOnly = false,
    this.required = false,
    this.maxLength,
    this.inputFormatters,
    this.validator,
    this.onChanged,
    this.onSubmitted,
    this.onTap,
    this.prefixIcon,
    this.focusNode,
    this.autofocus = false,
    this.variant = BanaTextFieldVariant.outlined,
    this.errorText,
    this.hasError = false,
  }) : obscureText = true,
       maxLines = 1,
       minLines = null,
       keyboardType = TextInputType.visiblePassword,
       textInputAction = TextInputAction.done,
       suffixIcon = null,
       onSuffixIconTap = null;

  /// Multiline text field constructor
  const BanaTextField.multiline({
    super.key,
    this.label,
    this.hint,
    this.initialValue,
    this.controller,
    this.enabled = true,
    this.readOnly = false,
    this.required = false,
    this.maxLines = 5,
    this.minLines = 3,
    this.maxLength,
    this.inputFormatters,
    this.validator,
    this.onChanged,
    this.onSubmitted,
    this.onTap,
    this.prefixIcon,
    this.suffixIcon,
    this.onSuffixIconTap,
    this.focusNode,
    this.autofocus = false,
    this.variant = BanaTextFieldVariant.outlined,
    this.errorText,
    this.hasError = false,
  }) : obscureText = false,
       keyboardType = TextInputType.multiline,
       textInputAction = TextInputAction.newline;

  @override
  State<BanaTextField> createState() => _BanaTextFieldState();
}

class _BanaTextFieldState extends State<BanaTextField> {
  late FocusNode _focusNode;
  late TextEditingController _controller;
  bool _obscureText = false;
  String? _errorText;

  @override
  void initState() {
    super.initState();
    _focusNode = widget.focusNode ?? FocusNode();
    _controller = widget.controller ?? TextEditingController(text: widget.initialValue);
    _obscureText = widget.obscureText;
    
    _focusNode.addListener(_onFocusChanged);
  }

  @override
  void dispose() {
    if (widget.focusNode == null) {
      _focusNode.dispose();
    }
    if (widget.controller == null) {
      _controller.dispose();
    }
    super.dispose();
  }

  void _onFocusChanged() {
    setState(() {});
  }

  void _onChanged(String value) {
    // Clear error when user starts typing
    if (_errorText != null) {
      setState(() => _errorText = null);
    }
    
    widget.onChanged?.call(value);
  }

  void _validate() {
    if (widget.validator != null) {
      final error = widget.validator!(_controller.text);
      setState(() => _errorText = error);
    }
  }

  void _toggleObscureText() {
    setState(() => _obscureText = !_obscureText);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label
        if (widget.label != null)
          _buildLabel(),
        
        if (widget.label != null) BanaSpacing.verticalSpacing.sm,
        
        // Text field
        _buildTextField(),
        
        // Error text
        if (_errorText != null || widget.errorText != null) ...[
          BanaSpacing.verticalSpacing.xs,
          _buildErrorText(),
        ],
      ],
    );
  }

  Widget _buildLabel() {
    return RichText(
      text: TextSpan(
        text: widget.label!,
        style: BanaTypography.labelLarge.copyWith(
          color: widget.enabled ? BanaColors.text : BanaColors.onDisabled,
        ),
        children: [
          if (widget.required)
            TextSpan(
              text: ' *',
              style: TextStyle(
                color: BanaColors.error,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildTextField() {
    final isFocused = _focusNode.hasFocus;
    final hasError = _errorText != null || widget.hasError;

    return Container(
      decoration: _buildContainerDecoration(isFocused, hasError),
      child: CupertinoTextField(
        controller: _controller,
        focusNode: _focusNode,
        enabled: widget.enabled,
        readOnly: widget.readOnly,
        obscureText: _obscureText,
        autofocus: widget.autofocus,
        maxLines: widget.obscureText ? 1 : widget.maxLines,
        minLines: widget.minLines,
        maxLength: widget.maxLength,
        keyboardType: widget.keyboardType,
        textInputAction: widget.textInputAction,
        inputFormatters: widget.inputFormatters,
        onChanged: _onChanged,
        onSubmitted: widget.onSubmitted,
        onTap: widget.onTap,
        onEditingComplete: _validate,
        style: BanaTypography.bodyLarge.copyWith(
          color: widget.enabled ? BanaColors.text : BanaColors.onDisabled,
        ),
        placeholder: widget.hint,
        placeholderStyle: BanaTypography.bodyLarge.copyWith(
          color: BanaColors.textTertiary,
        ),
        prefix: widget.prefixIcon != null
            ? Padding(
                padding: const EdgeInsets.only(left: 12.0, right: 8.0),
                child: Icon(
                  widget.prefixIcon,
                  color: hasError
                      ? BanaColors.error
                      : (isFocused ? BanaColors.primary : BanaColors.textSecondary),
                  size: 20,
                ),
              )
            : null,
        suffix: _buildSuffix(),
        decoration: const BoxDecoration(),
        padding: EdgeInsets.symmetric(
          horizontal: widget.prefixIcon == null ? 12.0 : 0.0,
          vertical: 12.0,
        ),
      ),
    );
  }

  BoxDecoration _buildContainerDecoration(bool isFocused, bool hasError) {
    Color borderColor;
    double borderWidth;

    if (hasError) {
      borderColor = BanaColors.error;
      borderWidth = BanaBorders.widthThick;
    } else if (isFocused) {
      borderColor = BanaColors.primary;
      borderWidth = BanaBorders.widthThick;
    } else {
      borderColor = widget.enabled ? BanaColors.border : BanaColors.disabled;
      borderWidth = BanaBorders.widthThin;
    }

    return BoxDecoration(
      color: widget.enabled ? BanaColors.surface : BanaColors.disabled.withValues(alpha: 0.1),
      borderRadius: _getBorderRadius(),
      border: Border.all(
        color: borderColor,
        width: borderWidth,
      ),
    );
  }

  BorderRadius _getBorderRadius() {
    switch (widget.variant) {
      case BanaTextFieldVariant.outlined:
        return BanaBorders.radius.md;
      case BanaTextFieldVariant.rounded:
        return BanaBorders.radius.circular;
    }
  }

  Widget? _buildSuffix() {
    if (widget.obscureText) {
      return GestureDetector(
        onTap: _toggleObscureText,
        child: Padding(
          padding: const EdgeInsets.only(right: 12.0, left: 8.0),
          child: Icon(
            _obscureText ? CupertinoIcons.eye : CupertinoIcons.eye_slash,
            color: BanaColors.textSecondary,
            size: 20,
          ),
        ),
      );
    }

    if (widget.suffixIcon != null) {
      return GestureDetector(
        onTap: widget.onSuffixIconTap,
        child: Padding(
          padding: const EdgeInsets.only(right: 12.0, left: 8.0),
          child: Icon(
            widget.suffixIcon,
            color: BanaColors.textSecondary,
            size: 20,
          ),
        ),
      );
    }

    return null;
  }

  Widget _buildErrorText() {
    final errorMessage = widget.errorText ?? _errorText;
    return Text(
      errorMessage!,
      style: BanaTypography.caption.copyWith(
        color: BanaColors.error,
      ),
    );
  }
}

/// Text field variant types
enum BanaTextFieldVariant {
  /// Standard outlined text field
  outlined,
  
  /// Rounded text field with circular borders
  rounded,
}

# Referral Code Error Handling Improvements

## Overview
Cải thiện xử lý lỗi và hiển thị thông báo cho tính năng referral code sử dụng BanaToast component.

## ✅ Improvements Implemented

### 1. **Enhanced Toast Messages in ReferralCodeScreen**

#### Success State
```dart
BanaToast.success(
  context: context,
  message: state.message ?? 'Áp dụng mã giới thiệu thành công!',
  duration: const Duration(seconds: 3),
);
```

#### Error State
```dart
BanaToast.error(
  context: context,
  message: state.message,
  duration: const Duration(seconds: 4),
  showCloseButton: true,
);
```

#### Info State (Already Has Code)
```dart
BanaToast.info(
  context: context,
  message: state.message,
  duration: const Duration(seconds: 3),
);
```

### 2. **Input Validation with Toast Warnings**

#### Empty Code Validation
```dart
if (code.isEmpty) {
  BanaToast.warning(
    context: context,
    message: '<PERSON><PERSON> lòng nhập mã giới thiệu',
    duration: const Duration(seconds: 2),
  );
  return;
}
```

#### Minimum Length Validation
```dart
if (code.length < 3) {
  BanaToast.warning(
    context: context,
    message: 'Mã giới thiệu phải có ít nhất 3 ký tự',
    duration: const Duration(seconds: 2),
  );
  return;
}
```

### 3. **Enhanced Error Messages in ReferralCubit**

#### Detailed HTTP Status Code Mapping
- **400**: "Mã giới thiệu không hợp lệ hoặc đã hết hạn. Vui lòng kiểm tra lại."
- **401**: "Phiên đăng nhập đã hết hạn. Vui lòng đăng nhập lại."
- **403**: "Bạn không có quyền sử dụng mã giới thiệu này."
- **404**: "Mã giới thiệu không tồn tại. Vui lòng kiểm tra lại."
- **409**: "Bạn đã sử dụng mã giới thiệu này rồi hoặc đã có mã khác."
- **422**: "Định dạng mã giới thiệu không đúng. Vui lòng nhập lại."
- **429**: "Bạn đã thử quá nhiều lần. Vui lòng đợi một chút rồi thử lại."
- **500/502/503**: "Lỗi hệ thống. Vui lòng thử lại sau ít phút."

### 4. **Improved Error Handling in ReferralService**

#### Enhanced Exception Handling
```dart
} catch (e, stackTrace) {
  _logger.e('Error applying referral code: $e', error: e, stackTrace: stackTrace);
  
  // Re-throw known exceptions
  if (e is ServerException || e is NetworkException) {
    rethrow;
  }
  
  // Handle parsing errors
  if (e is FormatException || e.toString().contains('FormatException')) {
    throw ServerException(
      'Dữ liệu phản hồi từ server không hợp lệ',
      statusCode: 500,
    );
  }
  
  // Handle timeout errors
  if (e.toString().contains('timeout') || e.toString().contains('TimeoutException')) {
    throw NetworkException(
      'Kết nối bị timeout. Vui lòng thử lại.',
    );
  }
  
  // Handle other unknown errors
  throw ServerException(
    'Có lỗi xảy ra khi xử lý mã giới thiệu. Vui lòng thử lại sau.',
    statusCode: 500,
  );
}
```

### 5. **Auto Navigation on Success**
- Hiển thị success toast
- Tự động navigate sau 1.5 giây
- Sử dụng `context.mounted` check để tránh memory leak

## 🎯 User Experience Improvements

### Visual Feedback
- ✅ **Success Toast**: Màu xanh với icon check, hiển thị 3 giây
- ❌ **Error Toast**: Màu đỏ với icon error, hiển thị 4 giây + close button
- ⚠️ **Warning Toast**: Màu vàng cho validation errors, hiển thị 2 giây
- ℹ️ **Info Toast**: Màu xanh dương cho thông tin, hiển thị 3 giây

### Haptic Feedback
- **Success**: `HapticFeedback.lightImpact()`
- **Error**: `HapticFeedback.heavyImpact()`

### Error Message Quality
- Thông báo lỗi rõ ràng, dễ hiểu
- Hướng dẫn cụ thể cho từng loại lỗi
- Ngôn ngữ thân thiện, không technical

## 🔧 Technical Benefits

### Robust Error Handling
- Xử lý tất cả loại exceptions (Server, Network, Parsing, Timeout)
- Detailed logging với stack trace
- Graceful fallback cho unknown errors

### Type Safety
- Null safety cho success messages
- Proper type checking cho exceptions
- Safe async operations với mounted checks

### Maintainability
- Centralized error message mapping
- Consistent toast usage patterns
- Clear separation of concerns

## 🚀 Next Steps

1. **Testing**: Test tất cả error scenarios
2. **Analytics**: Track error rates và types
3. **Localization**: Hỗ trợ đa ngôn ngữ cho error messages
4. **Retry Logic**: Thêm auto-retry cho network errors
5. **Offline Support**: Xử lý trường hợp offline

## 📱 Toast Types Available

```dart
// Success
BanaToast.success(context: context, message: 'Success message');

// Error  
BanaToast.error(context: context, message: 'Error message');

// Warning
BanaToast.warning(context: context, message: 'Warning message');

// Info
BanaToast.info(context: context, message: 'Info message');

// Custom
BanaToast.show(
  context: context,
  message: 'Custom message',
  icon: Icons.custom,
  backgroundColor: Colors.custom,
);
```

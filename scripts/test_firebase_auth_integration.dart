#!/usr/bin/env dart

import 'dart:io';
import 'dart:convert';

/// Integration test script for Firebase Auth implementation
/// 
/// This script validates:
/// 1. Firebase Auth service initialization
/// 2. API endpoint availability
/// 3. Token validation flow
/// 4. Error handling

void main() async {
  await testFirebaseConfiguration();
  await testAPIEndpoints();
  await testTokenValidation();

}

/// Test Firebase configuration
Future<void> testFirebaseConfiguration() async {
  
  try {
    // Check if Firebase configuration files exist
    final androidConfig = File('android/app/google-services.json');
    final iosConfig = File('ios/Runner/GoogleService-Info.plist');
    final firebaseOptions = File('lib/firebase_options.dart');
    
    if (androidConfig.existsSync()) {
      print('✅ Android Firebase config found');
      
      // Validate Android config
      final androidContent = await androidConfig.readAsString();
      final androidJson = jsonDecode(androidContent);
      
      if (androidJson['project_info']?['project_id'] == 'banachef-f03aa') {
        print('✅ Android config project ID matches');
      } else {
        print('❌ Android config project ID mismatch');
      }
    } else {
    }
    
    if (iosConfig.existsSync()) {
    } else {
    }
    
    if (firebaseOptions.existsSync()) {
      print('✅ Firebase options file found');
      
      // Check if it contains the correct project ID
      final optionsContent = await firebaseOptions.readAsString();
      if (optionsContent.contains('banachef-f03aa')) {
        print('✅ Firebase options project ID matches');
      } else {
        print('❌ Firebase options project ID mismatch');
      }
    } else {
      print('❌ Firebase options file missing');
    }
    
  } catch (e) {
    print('❌ Firebase configuration test failed: $e');
  }
}

/// Test API endpoints
Future<void> testAPIEndpoints() async {

  
  try {
    final client = HttpClient();
    
    // Test development server endpoints
    final devBaseUrl = 'http://localhost:8000/api/v1';
    
    await testEndpoint(client, '$devBaseUrl/auth/login/firebase', 'Firebase Auth Endpoint');
    await testEndpoint(client, '$devBaseUrl/auth/login/google', 'Google Auth Endpoint');
    await testEndpoint(client, '$devBaseUrl/auth/login/apple', 'Apple Auth Endpoint');
    await testEndpoint(client, '$devBaseUrl/auth/refresh', 'Token Refresh Endpoint');
    
    client.close();
    
  } catch (e) {
    print('❌ API endpoint test failed: $e');
  }
}

/// Test individual endpoint
Future<void> testEndpoint(HttpClient client, String url, String name) async {
  try {
    final uri = Uri.parse(url);
    final request = await client.postUrl(uri);
    request.headers.set('Content-Type', 'application/json');
    
    // Send empty request to check if endpoint exists
    request.write('{}');
    
    final response = await request.close();
    
    if (response.statusCode == 422 || response.statusCode == 400) {
      // 422 = Validation error (expected for empty request)
      // 400 = Bad request (expected for invalid data)
      print('✅ $name is available (status: ${response.statusCode})');
    } else if (response.statusCode == 404) {
      print('❌ $name not found (status: 404)');
    } else {
      print('⚠️  $name returned unexpected status: ${response.statusCode}');
    }
    
  } catch (e) {
    if (e.toString().contains('Connection refused')) {
      print('⚠️  $name - Server not running (expected in CI)');
    } else {
      print('❌ $name test failed: $e');
    }
  }
}

/// Test token validation logic
Future<void> testTokenValidation() async {
  print('\n🔐 Testing Token Validation Logic...');
  
  try {
    // Test JWT token format validation
    final validJwtPattern = RegExp(r'^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+$');
    
    // Test valid JWT format
    const testJwt = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c';
    
    if (validJwtPattern.hasMatch(testJwt)) {
      print('✅ JWT token format validation works');
    } else {
    }
    const testFirebaseToken = '******************************************************************************************************.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.test-signature';
    
    if (testFirebaseToken.length > 500) {
      print('✅ Firebase token format validation works');
    } else {
      print('❌ Firebase token format validation failed');
    }
    
    // Test token type detection logic
    if (isFirebaseToken(testFirebaseToken)) {
      print('✅ Firebase token detection works');
    } else {
      print('❌ Firebase token detection failed');
    }
    
    if (!isFirebaseToken(testJwt)) {
      print('✅ JWT token detection works');
    } else {
    }
    
  } catch (e) {
    print('❌ Token validation test failed: $e');
  }
}

/// Simple Firebase token detection logic
bool isFirebaseToken(String token) {
  try {
    final parts = token.split('.');
    if (parts.length != 3) return false;
    
    // Decode header to check issuer
    final header = parts[0];
    final paddedHeader = header + '=' * (4 - header.length % 4);
    final decodedHeader = utf8.decode(base64Url.decode(paddedHeader));
    final headerJson = jsonDecode(decodedHeader);
    
    // Firebase tokens typically use RS256
    return headerJson['alg'] == 'RS256';
  } catch (e) {
    return false;
  }
}
